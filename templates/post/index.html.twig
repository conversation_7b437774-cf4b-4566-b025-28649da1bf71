{% extends 'base.html.twig' %}

{% block title %}
    {% if currentCategory %}
        {{ currentCategory.name }} - Inzeráty - Infauna
    {% else %}
        Inzeráty - Infauna
    {% endif %}
{% endblock %}

{% block body %}
<div class="min-h-screen bg-white">
    <!-- Navigation -->
    {{ include('_partials/navigation.html.twig') }}

    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center space-x-6">
                    <h1 class="text-2xl font-semibold text-gray-900">
                        {% if currentCategory %}
                            {{ currentCategory.name }}
                        {% else %}
                            Inzeráty
                        {% endif %}
                    </h1>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-500" data-filter-target="count">
                            {% if totalCount == 1 %}
                                {{ totalCount }} inzerát
                            {% elseif totalCount < 5 %}
                                {{ totalCount }} inzeráty
                            {% else %}
                                {{ totalCount }} inzerátů
                            {% endif %}
                        </span>
                        {% if activeFiltersCount > 0 %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700">
                                {{ activeFiltersCount }} {% if activeFiltersCount == 1 %}filtr{% elseif activeFiltersCount < 5 %}filtry{% else %}filtrů{% endif %}
                            </span>
                        {% endif %}
                    </div>
                </div>
                <div class="flex space-x-3">
                    {% if app.user %}
                        <a href="{{ path('app_user_post_new') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Přidat inzerát
                        </a>
                    {% endif %}
                    <a href="{{ path('app_home') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        ← Zpět
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content with Sidebar -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8"
             data-controller="filter"
             data-filter-base-url-value="{{ path('app_posts') }}"
             data-filter-current-category-value="{{ currentCategory ? currentCategory.slug : '' }}">

            <!-- Sidebar s filtry -->
            <div class="lg:w-64 flex-shrink-0">
                <div class="bg-white border border-gray-200 rounded-lg p-6 sticky top-8">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Filtry</h3>
                        {% if activeFiltersCount > 0 %}
                            <button type="button"
                                    onclick="this.closest('[data-controller=filter]').querySelector('[data-action=filter#clearAllFilters]').click()"
                                    class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                Vymazat vše
                            </button>
                        {% endif %}
                    </div>

                    <form data-filter-target="form" class="space-y-6">
                        <!-- Kategorie -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Kategorie</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded">
                                    <div class="flex items-center">
                                        <input type="radio"
                                               name="category"
                                               value="all"
                                               data-filter-target="categoryRadio"
                                               {% if not currentCategory %}checked{% endif %}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                        <span class="ml-3 text-sm text-gray-700">Všechny kategorie</span>
                                    </div>
                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ filterData.totalCount }}</span>
                                </label>

                                {% for categoryData in filterData.categories %}
                                    {% set category = categoryData.entity %}
                                    {% set count = categoryData.count %}
                                    <label class="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded {% if count == 0 %}opacity-50{% endif %}">
                                        <div class="flex items-center">
                                            <input type="radio"
                                                   name="category"
                                                   value="{{ category.slug }}"
                                                   data-filter-target="categoryRadio"
                                                   {% if currentCategory and currentCategory.id == category.id %}checked{% endif %}
                                                   {% if count == 0 %}disabled{% endif %}
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                            <span class="ml-3 text-sm {% if count == 0 %}text-gray-400{% else %}text-gray-700{% endif %}">{{ category.name }}</span>
                                        </div>
                                        <span class="text-xs {% if count == 0 %}text-gray-400 bg-gray-50{% else %}text-gray-500 bg-gray-100{% endif %} px-2 py-1 rounded">{{ count }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Plemena -->
                        {% if filterData.breeds|length > 0 %}
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Plemena</h4>
                                <div class="space-y-1 max-h-64 overflow-y-auto">
                                    {% for breedData in filterData.breeds %}
                                        {% set breed = breedData.entity %}
                                        {% set count = breedData.count %}
                                        <label class="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded filter-option  {% if count == 0 %}opacity-50{% endif %}" data-category="{{ breed.category.slug }}">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       name="breeds[]"
                                                       value="{{ breed.id }}"
                                                       data-filter-target="breedCheckbox"
                                                       data-category="{{ breed.category.slug }}"
                                                       {% if currentFilters.breeds is defined %}
                                                           {% for selectedBreed in currentFilters.breeds %}
                                                               {% if selectedBreed.id == breed.id %}checked{% endif %}
                                                           {% endfor %}
                                                       {% endif %}
                                                       {% if count == 0 %}disabled{% endif %}
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <span class="ml-3 text-sm {% if count == 0 %}text-gray-400{% else %}text-gray-700{% endif %}">{{ breed.name }}</span>
                                            </div>
                                            <span class="text-xs {% if count == 0 %}text-gray-400 bg-gray-50{% else %}text-gray-500 bg-gray-100{% endif %} px-2 py-1 rounded flex-shrink-0">{{ count }}</span>
                                        </label>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Kraje -->
                        {% if filterData.regions|length > 0 %}
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Kraje</h4>
                                <div class="space-y-2 max-h-48 overflow-y-auto">
                                    {% for regionData in filterData.regions %}
                                        {% set region = regionData.entity %}
                                        {% set count = regionData.count %}
                                        <label class="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded {% if count == 0 %}opacity-50{% endif %}">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       name="regions[]"
                                                       value="{{ region.id }}"
                                                       data-filter-target="regionCheckbox"
                                                       {% if currentFilters.regions is defined %}
                                                           {% for selectedRegion in currentFilters.regions %}
                                                               {% if selectedRegion.id == region.id %}checked{% endif %}
                                                           {% endfor %}
                                                       {% endif %}
                                                       {% if count == 0 %}disabled{% endif %}
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <span class="ml-3 text-sm {% if count == 0 %}text-gray-400{% else %}text-gray-700{% endif %}">{{ region.name }}</span>
                                            </div>
                                            <span class="text-xs {% if count == 0 %}text-gray-400 bg-gray-50{% else %}text-gray-500 bg-gray-100{% endif %} px-2 py-1 rounded">{{ count }}</span>
                                        </label>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Typ inzerátu -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Typ inzerátu</h4>
                            <div class="space-y-2">
                                {% for postTypeData in filterData.postTypes %}
                                    {% set postType = postTypeData.type %}
                                    {% set count = postTypeData.count %}
                                    <label class="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded {% if count == 0 %}opacity-50{% endif %}">
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   name="types[]"
                                                   value="{{ postType.value }}"
                                                   data-filter-target="typeCheckbox"
                                                   {% if currentFilters.types is defined %}
                                                       {% for selectedType in currentFilters.types %}
                                                           {% if selectedType.value == postType.value %}checked{% endif %}
                                                       {% endfor %}
                                                   {% endif %}
                                                   {% if count == 0 %}disabled{% endif %}
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <span class="ml-3 text-sm {% if count == 0 %}text-gray-400{% else %}text-gray-700{% endif %}">{{ postType.label }}</span>
                                        </div>
                                        <span class="text-xs {% if count == 0 %}text-gray-400 bg-gray-50{% else %}text-gray-500 bg-gray-100{% endif %} px-2 py-1 rounded">{{ count }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                        </div>



                        <!-- Skryté tlačítko pro vymazání filtrů -->
                        <button type="button"
                                data-action="filter#clearAllFilters"
                                class="hidden">
                        </button>
                    </form>
                </div>
            </div>

            <!-- Hlavní obsah -->
            <div class="flex-1">
                <div data-filter-target="results">
                    {% if posts|length > 0 %}
                        <div class="space-y-6">
                            {% for post in posts %}
                                <article class="bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow overflow-hidden">
                                    <div class="flex">
                                        <!-- Obrázek -->
                                        <div class="w-48 h-32 flex-shrink-0">
                                            {% if post.mainImage %}
                                                <img src="{{ post.mainImage.imageUrl }}" alt="{{ post.title }}" class="w-full h-full object-cover">
                                            {% else %}
                                                <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                                    <div class="text-gray-400 text-center">
                                                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                                        </svg>
                                                        <p class="text-xs">{{ post.category.name }}</p>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>

                                        <!-- Obsah -->
                                        <div class="flex-1 p-6">
                                            <div class="flex items-start justify-between mb-3">
                                                <div class="flex items-center space-x-2">
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium
                                                        {% if post.postType.value == 'sell' %}bg-green-100 text-green-700
                                                        {% elseif post.postType.value == 'buy' %}bg-blue-100 text-blue-700
                                                        {% else %}bg-purple-100 text-purple-700
                                                        {% endif %}">
                                                        {{ post.postType.label }}
                                                    </span>
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                                        {{ post.category.name }}
                                                    </span>
                                                </div>
                                                {% if post.formattedPrice %}
                                                    <div class="text-right">
                                                        <span class="text-xl font-semibold text-gray-900">{{ post.formattedPrice }}</span>
                                                    </div>
                                                {% endif %}
                                            </div>

                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                                <a href="{{ path('app_post_detail', {slug: post.slug}) }}" class="hover:text-blue-600 transition-colors">
                                                    {{ post.title }}
                                                </a>
                                            </h3>

                                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ post.description|slice(0, 150) }}{% if post.description|length > 150 %}...{% endif %}</p>

                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                                    <span>{{ post.region ? post.region.name : post.country.label }}</span>
                                                    {% if post.breed %}
                                        <span>{{ post.breed.name }}</span>
                                    {% endif %}
                                                    <span>{{ post.createdAt|date('d.m.Y') }}</span>
                                                </div>
                                                <a href="{{ path('app_post_detail', {slug: post.slug}) }}"
                                                   class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors">
                                                    Zobrazit detail →
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="bg-white border border-gray-200 rounded-lg p-12 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Žádné inzeráty</h3>
                            <p class="text-gray-500 mb-6">
                                {% if activeFiltersCount > 0 %}
                                    Žádné inzeráty nevyhovují vybraným filtrům. Zkuste změnit kritéria vyhledávání.
                                {% else %}
                                    Momentálně nejsou k dispozici žádné aktivní inzeráty.
                                {% endif %}
                            </p>
                            {% if activeFiltersCount > 0 %}
                                <button type="button"
                                        onclick="this.closest('[data-controller=filter]').querySelector('[data-action=filter#clearAllFilters]').click()"
                                        class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors">
                                    Vymazat všechny filtry
                                </button>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
